import { Op } from 'sequelize';
import { responseModel } from '../../models';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';

interface ResponseFilters {
  timeFilter?: string;
  customStartDate?: string;
  customEndDate?: string;
  page?: number;
  limit?: number;
}

/**
 * Data Access Layer function to retrieve survey responses
 *
 * This function fetches responses for a specific survey with filtering and pagination support.
 * Used for displaying responses on the dashboard.
 *
 * Security features:
 * - Validates account ownership by including account_id in query
 * - Comprehensive error handling with logging
 *
 * Performance considerations:
 * - Orders by creation date (newest first) for better UX
 * - Supports pagination to handle large datasets
 * - Uses indexed fields (survey_id, account_id) for optimal query performance
 *
 * @param surveyId - The unique UUID of the survey whose responses to retrieve
 * @param accountId - The unique UUID of the account that owns the survey
 * @param filters - Optional filters for responses
 * @returns Promise<{success: boolean, message: string, payload: any}> - Operation result with responses
 *
 * @example
 * const result = await readResponses(surveyId, accountId, { page: 1, limit: 10 });
 * if (result.success) {
 *   console.log(`Found ${result.payload.responses.length} responses`);
 * }
 */
export const readResponses: any = async (surveyId: string, accountId: string, filters: ResponseFilters = {}) => {
  try {
    const { timeFilter, customStartDate, customEndDate, page = 1, limit = 10 } = filters;

    // Build where conditions
    const whereConditions: any = {
      survey_id: surveyId,
      account_id: accountId, // Security: Ensure account owns the survey
      is_deleted: false, // Only show non-deleted responses
    };

    // Apply time filter if provided
    if (timeFilter && timeFilter !== 'all-time') {
      if (timeFilter === 'custom-range' && customStartDate && customEndDate) {
        // Handle custom date range
        const startDate = new Date(customStartDate);
        const endDate = new Date(customEndDate);
        // Set end date to end of day
        endDate.setHours(23, 59, 59, 999);

        whereConditions.created_at = {
          [Op.between]: [startDate, endDate],
        };
      } else {
        // Handle predefined time filters
        const now = new Date();
        let startDate: Date;

        switch (timeFilter) {
          case 'last-24-hours':
            startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
          case '7-days':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30-days':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '90-days':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          default:
            startDate = new Date(0); // Default to all time
        }

        whereConditions.created_at = {
          [Op.gte]: startDate,
        };
      }
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await responseModel.count({
      where: whereConditions,
    });

    // Fetch responses with pagination
    const responseData = await responseModel.findAll({
      where: whereConditions,
      order: [['created_at', 'DESC']], // Newest first
      limit,
      offset,
      attributes: ['response_data', 'respondent_details', 'meta', 'created_at', 'is_deleted', 'delete_reason'],
    });

    // Transform responses to array format
    const responses: any[] = responseData.map(responseData => {
      // Clean meta data - remove sensitive information
      const cleanMeta = responseData.dataValues.meta
        ? {
            ...responseData.dataValues.meta,
            // Remove any sensitive fields if they exist
            ip: undefined,
            // Keep userAgent for debugging and analytics purposes
          }
        : {};

      return {
        // Exclude response ID for privacy
        response_data: responseData.dataValues.response_data,
        respondent_details: responseData.dataValues.respondent_details,
        meta: cleanMeta,
        created_at: responseData.dataValues.created_at,
        is_deleted: responseData.dataValues.is_deleted,
        delete_reason: responseData.dataValues.delete_reason,
      };
    });

    // Generate analytics data
    const analytics = await generateAnalytics(surveyId, accountId, timeFilter, customStartDate, customEndDate);

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses retrieved`,
      payload: {
        responses,
        totalCount,
        totalResponses: analytics.totalResponses,
        avgCompletionTime: analytics.avgCompletionTime,
        oldestResponseTimestamp: analytics.oldestResponseTimestamp,
        responsesByDay: analytics.responsesByDay,
        responseTimestamps: analytics.responseTimestamps,
        responseRateTrend: analytics.responseRateTrend,
      },
    };
  } catch (error) {
    // Log error for debugging and monitoring
    logger.error('Error retrieving responses:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve responses`,
      payload: error,
    };
  }
};

/**
 * Generate analytics data for survey responses
 *
 * @param surveyId - Survey ID to generate analytics for
 * @param accountId - Account ID for security validation
 * @param timeFilter - Optional time filter
 * @param customStartDate - Optional custom start date
 * @param customEndDate - Optional custom end date
 * @returns Analytics object with response metrics
 */
const generateAnalytics = async (surveyId: string, accountId: string, timeFilter?: string, customStartDate?: string, customEndDate?: string) => {
  try {
    // Build where conditions for analytics
    const whereConditions: any = {
      survey_id: surveyId,
      account_id: accountId,
      is_deleted: false,
    };

    let currentPeriodStart: Date;
    let currentPeriodEnd: Date;
    let previousPeriodStart: Date;
    let previousPeriodEnd: Date;
    let trendLabel: string;

    // Apply time filter and calculate periods for trend comparison
    if (timeFilter && timeFilter !== 'all-time') {
      if (timeFilter === 'custom-range' && customStartDate && customEndDate) {
        // Handle custom date range
        currentPeriodStart = new Date(customStartDate);
        currentPeriodEnd = new Date(customEndDate);
        // Set end date to end of day
        currentPeriodEnd.setHours(23, 59, 59, 999);

        // Calculate previous period of same duration
        const rangeDuration = currentPeriodEnd.getTime() - currentPeriodStart.getTime();
        previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1); // 1ms before current period
        previousPeriodStart = new Date(previousPeriodEnd.getTime() - rangeDuration);
        trendLabel = 'vs previous period';

        whereConditions.created_at = {
          [Op.between]: [currentPeriodStart, currentPeriodEnd],
        };
      } else {
        // Handle predefined time filters
        const now = new Date();
        currentPeriodEnd = now;

        switch (timeFilter) {
          case 'last-24-hours':
            currentPeriodStart = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
            previousPeriodStart = new Date(previousPeriodEnd.getTime() - 24 * 60 * 60 * 1000);
            trendLabel = 'vs yesterday';
            break;
          case '7-days':
            currentPeriodStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
            previousPeriodStart = new Date(previousPeriodEnd.getTime() - 7 * 24 * 60 * 60 * 1000);
            trendLabel = 'vs last week';
            break;
          case '30-days':
            currentPeriodStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
            previousPeriodStart = new Date(previousPeriodEnd.getTime() - 30 * 24 * 60 * 60 * 1000);
            trendLabel = 'vs last month';
            break;
          case '90-days':
            currentPeriodStart = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            previousPeriodEnd = new Date(currentPeriodStart.getTime() - 1);
            previousPeriodStart = new Date(previousPeriodEnd.getTime() - 90 * 24 * 60 * 60 * 1000);
            trendLabel = 'vs previous quarter';
            break;
          default:
            currentPeriodStart = new Date(0);
            previousPeriodStart = new Date(0);
            previousPeriodEnd = new Date(0);
            trendLabel = '';
        }

        whereConditions.created_at = {
          [Op.gte]: currentPeriodStart,
        };
      }
    } else {
      // For 'all-time', we don't calculate trend
      currentPeriodStart = new Date(0);
      currentPeriodEnd = new Date();
      previousPeriodStart = new Date(0);
      previousPeriodEnd = new Date(0);
      trendLabel = '';
    }

    // Get all responses for current period analytics
    const allResponses = await responseModel.findAll({
      where: whereConditions,
      attributes: ['created_at', 'meta'],
      order: [['created_at', 'ASC']],
    });

    const totalResponses = allResponses.length;
    let avgCompletionTime = 0;
    const responsesByDay: any = {};
    const responseTimestamps: string[] = [];

    allResponses.forEach(response => {
      // Calculate completion time if available in meta
      if (response.dataValues.meta?.completionTime) {
        avgCompletionTime += response.dataValues.meta.completionTime;
      }

      // Group by day for chart data
      const dayKey = response.dataValues.created_at.toISOString().split('T')[0];
      responsesByDay[dayKey] = (responsesByDay[dayKey] || 0) + 1;

      // Add timestamp to array (always use full ISO string for array)
      responseTimestamps.push(response.dataValues.created_at.toISOString());
    });

    // Calculate average completion time
    if (totalResponses > 0 && avgCompletionTime > 0) {
      avgCompletionTime = Math.round(avgCompletionTime / totalResponses);
    }

    // Calculate response rate trend
    let responseRateTrend = '';
    if (timeFilter && timeFilter !== 'all-time' && trendLabel) {
      // Get previous period responses for comparison
      const previousPeriodResponses = await responseModel.findAll({
        where: {
          survey_id: surveyId,
          account_id: accountId,
          is_deleted: false,
          created_at: {
            [Op.between]: [previousPeriodStart, previousPeriodEnd],
          },
        },
        attributes: ['created_at'],
      });

      const previousPeriodCount = previousPeriodResponses.length;

      // Calculate trend with edge case handling
      if (previousPeriodCount === 0 && totalResponses === 0) {
        responseRateTrend = 'No responses yet';
      } else if (previousPeriodCount === 0 && totalResponses > 0) {
        responseRateTrend = 'New responses this period';
      } else if (previousPeriodCount > 0 && totalResponses === 0) {
        responseRateTrend = '-100% ' + trendLabel;
      } else if (previousPeriodCount === totalResponses) {
        responseRateTrend = 'No change ' + trendLabel;
      } else {
        const percentChange = Math.round(((totalResponses - previousPeriodCount) / previousPeriodCount) * 100);
        const sign = percentChange > 0 ? '+' : '';
        responseRateTrend = `${sign}${percentChange}% ${trendLabel}`;
      }
    } else if (timeFilter === 'all-time') {
      // For all-time, show average responses per day
      if (totalResponses > 0 && allResponses.length > 0) {
        const firstResponseDate = new Date(allResponses[0].dataValues.created_at);
        const daysSinceFirst = Math.max(1, Math.ceil((new Date().getTime() - firstResponseDate.getTime()) / (24 * 60 * 60 * 1000)));
        const avgPerDay = (totalResponses / daysSinceFirst).toFixed(1);
        responseRateTrend = `${avgPerDay} responses/day avg`;
      } else {
        responseRateTrend = 'No responses yet';
      }
    }

    // Convert responsesByDay object to array format with dates and counts
    const responsesByDayArray = Object.entries(responsesByDay)
      .map(([date, count]) => ({
        date,
        count,
      }))
      .sort((a, b) => a.date.localeCompare(b.date)); // Sort by date ascending

    // Get the oldest response timestamp for date range setting
    const oldestResponseTimestamp = allResponses.length > 0 ? allResponses[0].dataValues.created_at.toISOString() : null;

    return {
      totalResponses,
      avgCompletionTime,
      responsesByDay: responsesByDayArray,
      oldestResponseTimestamp,
      responseTimestamps,
      responseRateTrend,
    };
  } catch (error) {
    logger.error('Error generating analytics:', error);
    return {
      totalResponses: 0,
      avgCompletionTime: 0,
      responsesByDay: [],
      oldestResponseTimestamp: null,
      responseTimestamps: [],
      responseRateTrend: 'Unable to calculate trend',
    };
  }
};
